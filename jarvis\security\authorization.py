"""
Authorization System for JARVIS AI Assistant
"""

import logging
from typing import Dict, List, Set, Optional, Tuple, Any
from dataclasses import dataclass, field
from datetime import datetime
import json
from pathlib import Path
from enum import Enum

from ..config import config

logger = logging.getLogger(__name__)


class PermissionLevel(Enum):
    """Permission levels"""
    NONE = 0
    READ = 1
    WRITE = 2
    EXECUTE = 3
    ADMIN = 4


@dataclass
class Permission:
    """Permission representation"""
    name: str
    description: str
    level: PermissionLevel
    resource_type: str
    conditions: Dict[str, Any] = field(default_factory=dict)


@dataclass
class Role:
    """Role representation"""
    role_id: str
    name: str
    description: str
    permissions: Set[str] = field(default_factory=set)
    is_system_role: bool = False
    created_at: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)


class AuthorizationManager:
    """Comprehensive authorization and permission management"""
    
    def __init__(self, data_dir: Optional[str] = None):
        """Initialize authorization manager"""
        self.data_dir = Path(data_dir or config.get("data_dir", "data"))
        self.permissions_file = self.data_dir / "permissions.json"
        self.roles_file = self.data_dir / "roles.json"
        
        # Ensure data directory exists
        self.data_dir.mkdir(exist_ok=True)
        
        # Permission and role storage
        self.permissions: Dict[str, Permission] = {}
        self.roles: Dict[str, Role] = {}
        self.user_roles: Dict[str, Set[str]] = {}  # user_id -> role_ids
        
        # Initialize default permissions and roles
        self._initialize_default_permissions()
        self._initialize_default_roles()
        
        # Load existing data
        self._load_permissions()
        self._load_roles()
        
        logger.info("Authorization manager initialized")
    
    def _initialize_default_permissions(self):
        """Initialize default system permissions"""
        default_permissions = [
            # Basic permissions
            Permission("basic_commands", "Execute basic commands", PermissionLevel.EXECUTE, "command"),
            Permission("help_access", "Access help and documentation", PermissionLevel.READ, "system"),
            Permission("status_check", "Check system status", PermissionLevel.READ, "system"),
            
            # File system permissions
            Permission("file_read", "Read files and directories", PermissionLevel.READ, "filesystem"),
            Permission("file_write", "Write and modify files", PermissionLevel.WRITE, "filesystem"),
            Permission("file_delete", "Delete files and directories", PermissionLevel.WRITE, "filesystem"),
            Permission("file_execute", "Execute files and scripts", PermissionLevel.EXECUTE, "filesystem"),
            
            # System permissions
            Permission("system_info", "Access system information", PermissionLevel.READ, "system"),
            Permission("system_monitor", "Monitor system resources", PermissionLevel.READ, "system"),
            Permission("system_control", "Control system processes", PermissionLevel.EXECUTE, "system"),
            Permission("system_config", "Modify system configuration", PermissionLevel.WRITE, "system"),
            
            # Network permissions
            Permission("web_search", "Perform web searches", PermissionLevel.EXECUTE, "network"),
            Permission("web_browse", "Browse websites", PermissionLevel.EXECUTE, "network"),
            Permission("api_access", "Access external APIs", PermissionLevel.EXECUTE, "network"),
            Permission("network_config", "Configure network settings", PermissionLevel.WRITE, "network"),
            
            # Application permissions
            Permission("app_launch", "Launch applications", PermissionLevel.EXECUTE, "application"),
            Permission("app_control", "Control running applications", PermissionLevel.EXECUTE, "application"),
            Permission("app_install", "Install applications", PermissionLevel.WRITE, "application"),
            
            # Voice permissions
            Permission("voice_input", "Use voice input", PermissionLevel.EXECUTE, "voice"),
            Permission("voice_output", "Use voice output", PermissionLevel.EXECUTE, "voice"),
            Permission("voice_config", "Configure voice settings", PermissionLevel.WRITE, "voice"),
            
            # Data permissions
            Permission("data_read", "Read user data", PermissionLevel.READ, "data"),
            Permission("data_write", "Write user data", PermissionLevel.WRITE, "data"),
            Permission("data_export", "Export user data", PermissionLevel.READ, "data"),
            Permission("data_import", "Import user data", PermissionLevel.WRITE, "data"),
            
            # Admin permissions
            Permission("user_management", "Manage user accounts", PermissionLevel.ADMIN, "admin"),
            Permission("role_management", "Manage roles and permissions", PermissionLevel.ADMIN, "admin"),
            Permission("system_admin", "Full system administration", PermissionLevel.ADMIN, "admin"),
            Permission("audit_access", "Access audit logs", PermissionLevel.READ, "admin"),
            Permission("security_config", "Configure security settings", PermissionLevel.ADMIN, "admin")
        ]
        
        for permission in default_permissions:
            self.permissions[permission.name] = permission
    
    def _initialize_default_roles(self):
        """Initialize default system roles"""
        default_roles = [
            # Basic user role
            Role(
                role_id="basic_user",
                name="Basic User",
                description="Basic user with limited permissions",
                permissions={
                    "basic_commands", "help_access", "status_check",
                    "file_read", "system_info", "web_search", "voice_input", "voice_output"
                },
                is_system_role=True
            ),
            
            # Power user role
            Role(
                role_id="power_user",
                name="Power User",
                description="Advanced user with extended permissions",
                permissions={
                    "basic_commands", "help_access", "status_check",
                    "file_read", "file_write", "system_info", "system_monitor",
                    "web_search", "web_browse", "api_access", "app_launch", "app_control",
                    "voice_input", "voice_output", "voice_config", "data_read", "data_write"
                },
                is_system_role=True
            ),
            
            # Administrator role
            Role(
                role_id="administrator",
                name="Administrator",
                description="Full system administrator",
                permissions=set(self.permissions.keys()),  # All permissions
                is_system_role=True
            ),
            
            # Guest role
            Role(
                role_id="guest",
                name="Guest",
                description="Limited guest access",
                permissions={
                    "basic_commands", "help_access", "status_check", "web_search"
                },
                is_system_role=True
            )
        ]
        
        for role in default_roles:
            self.roles[role.role_id] = role
    
    def check_permission(
        self,
        user_id: str,
        permission: str,
        resource: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> bool:
        """Check if user has specific permission"""
        try:
            # Get user roles
            user_roles = self.user_roles.get(user_id, set())
            
            # Check if user has permission through any role
            for role_id in user_roles:
                if role_id in self.roles:
                    role = self.roles[role_id]
                    if permission in role.permissions:
                        # Check permission conditions if any
                        if self._check_permission_conditions(permission, resource, context):
                            return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking permission: {e}")
            return False
    
    def check_multiple_permissions(
        self,
        user_id: str,
        permissions: List[str],
        require_all: bool = True
    ) -> bool:
        """Check multiple permissions"""
        results = [self.check_permission(user_id, perm) for perm in permissions]
        
        if require_all:
            return all(results)
        else:
            return any(results)
    
    def get_user_permissions(self, user_id: str) -> Set[str]:
        """Get all permissions for a user"""
        user_permissions = set()
        user_roles = self.user_roles.get(user_id, set())
        
        for role_id in user_roles:
            if role_id in self.roles:
                role = self.roles[role_id]
                user_permissions.update(role.permissions)
        
        return user_permissions
    
    def assign_role(self, user_id: str, role_id: str) -> Tuple[bool, str]:
        """Assign role to user"""
        try:
            if role_id not in self.roles:
                return False, "Role not found"
            
            if user_id not in self.user_roles:
                self.user_roles[user_id] = set()
            
            self.user_roles[user_id].add(role_id)
            self._save_user_roles()
            
            logger.info(f"Assigned role {role_id} to user {user_id}")
            return True, "Role assigned successfully"
            
        except Exception as e:
            logger.error(f"Error assigning role: {e}")
            return False, str(e)
    
    def revoke_role(self, user_id: str, role_id: str) -> Tuple[bool, str]:
        """Revoke role from user"""
        try:
            if user_id not in self.user_roles:
                return False, "User has no roles"
            
            if role_id not in self.user_roles[user_id]:
                return False, "User does not have this role"
            
            self.user_roles[user_id].remove(role_id)
            self._save_user_roles()
            
            logger.info(f"Revoked role {role_id} from user {user_id}")
            return True, "Role revoked successfully"
            
        except Exception as e:
            logger.error(f"Error revoking role: {e}")
            return False, str(e)
    
    def create_role(
        self,
        role_id: str,
        name: str,
        description: str,
        permissions: Set[str]
    ) -> Tuple[bool, str]:
        """Create new role"""
        try:
            if role_id in self.roles:
                return False, "Role already exists"
            
            # Validate permissions
            invalid_permissions = permissions - set(self.permissions.keys())
            if invalid_permissions:
                return False, f"Invalid permissions: {invalid_permissions}"
            
            role = Role(
                role_id=role_id,
                name=name,
                description=description,
                permissions=permissions,
                is_system_role=False
            )
            
            self.roles[role_id] = role
            self._save_roles()
            
            logger.info(f"Created role: {role_id}")
            return True, "Role created successfully"
            
        except Exception as e:
            logger.error(f"Error creating role: {e}")
            return False, str(e)
    
    def update_role(
        self,
        role_id: str,
        name: Optional[str] = None,
        description: Optional[str] = None,
        permissions: Optional[Set[str]] = None
    ) -> Tuple[bool, str]:
        """Update existing role"""
        try:
            if role_id not in self.roles:
                return False, "Role not found"
            
            role = self.roles[role_id]
            
            # Don't allow modification of system roles
            if role.is_system_role:
                return False, "Cannot modify system roles"
            
            # Update fields
            if name is not None:
                role.name = name
            if description is not None:
                role.description = description
            if permissions is not None:
                # Validate permissions
                invalid_permissions = permissions - set(self.permissions.keys())
                if invalid_permissions:
                    return False, f"Invalid permissions: {invalid_permissions}"
                role.permissions = permissions
            
            self._save_roles()
            
            logger.info(f"Updated role: {role_id}")
            return True, "Role updated successfully"
            
        except Exception as e:
            logger.error(f"Error updating role: {e}")
            return False, str(e)
    
    def delete_role(self, role_id: str) -> Tuple[bool, str]:
        """Delete role"""
        try:
            if role_id not in self.roles:
                return False, "Role not found"
            
            role = self.roles[role_id]
            
            # Don't allow deletion of system roles
            if role.is_system_role:
                return False, "Cannot delete system roles"
            
            # Remove role from all users
            for user_id, user_roles in self.user_roles.items():
                if role_id in user_roles:
                    user_roles.remove(role_id)
            
            # Delete role
            del self.roles[role_id]
            self._save_roles()
            self._save_user_roles()
            
            logger.info(f"Deleted role: {role_id}")
            return True, "Role deleted successfully"
            
        except Exception as e:
            logger.error(f"Error deleting role: {e}")
            return False, str(e)
    
    def get_role_info(self, role_id: str) -> Optional[Dict]:
        """Get role information"""
        if role_id not in self.roles:
            return None
        
        role = self.roles[role_id]
        return {
            "role_id": role.role_id,
            "name": role.name,
            "description": role.description,
            "permissions": list(role.permissions),
            "is_system_role": role.is_system_role,
            "created_at": role.created_at.isoformat(),
            "user_count": sum(1 for user_roles in self.user_roles.values() if role_id in user_roles)
        }
    
    def list_roles(self) -> List[Dict]:
        """List all roles"""
        role_infos = []
        for role_id in self.roles.keys():
            role_info = self.get_role_info(role_id)
            if role_info is not None:
                role_infos.append(role_info)
        return role_infos
    
    def list_permissions(self) -> List[Dict]:
        """List all permissions"""
        return [
            {
                "name": perm.name,
                "description": perm.description,
                "level": perm.level.name,
                "resource_type": perm.resource_type,
                "conditions": perm.conditions
            }
            for perm in self.permissions.values()
        ]
    
    def get_user_roles(self, user_id: str) -> List[str]:
        """Get roles assigned to user"""
        return list(self.user_roles.get(user_id, set()))
    
    def get_authorization_summary(self, user_id: str) -> Dict[str, Any]:
        """Get comprehensive authorization summary for user"""
        user_roles = self.get_user_roles(user_id)
        user_permissions = self.get_user_permissions(user_id)
        
        # Group permissions by resource type
        permissions_by_type = {}
        for perm_name in user_permissions:
            if perm_name in self.permissions:
                perm = self.permissions[perm_name]
                if perm.resource_type not in permissions_by_type:
                    permissions_by_type[perm.resource_type] = []
                permissions_by_type[perm.resource_type].append({
                    "name": perm.name,
                    "level": perm.level.name
                })
        
        return {
            "user_id": user_id,
            "roles": [
                {
                    "role_id": role_id,
                    "name": self.roles[role_id].name if role_id in self.roles else "Unknown"
                }
                for role_id in user_roles
            ],
            "total_permissions": len(user_permissions),
            "permissions_by_type": permissions_by_type,
            "is_admin": (
                "administrator" in user_roles or
                "system_admin" in user_permissions
            )
        }
    
    def _check_permission_conditions(
        self,
        permission: str,
        resource: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> bool:
        """Check permission-specific conditions"""
        if permission not in self.permissions:
            return False
        
        perm = self.permissions[permission]
        conditions = perm.conditions
        
        if not conditions:
            return True
        
        # Check time-based conditions
        if "time_restrictions" in conditions:
            # Implementation for time-based access control
            pass
        
        # Check resource-based conditions
        if "resource_patterns" in conditions and resource:
            # Implementation for resource pattern matching
            pass
        
        # Check context-based conditions
        if "context_requirements" in conditions and context:
            # Implementation for context-based access control
            pass
        
        return True
    
    def _save_user_roles(self):
        """Save user roles to file"""
        try:
            user_roles_data = {}
            for user_id, roles in self.user_roles.items():
                user_roles_data[user_id] = list(roles)
            
            user_roles_file = self.data_dir / "user_roles.json"
            with open(user_roles_file, 'w') as f:
                json.dump(user_roles_data, f, indent=2)
                
        except Exception as e:
            logger.error(f"Error saving user roles: {e}")
    
    def _load_permissions(self):
        """Load permissions from file"""
        try:
            if self.permissions_file.exists():
                with open(self.permissions_file, 'r') as f:
                    permissions_data = json.load(f)
                
                for perm_name, perm_dict in permissions_data.items():
                    perm_dict["level"] = PermissionLevel[perm_dict["level"]]
                    permission = Permission(**perm_dict)
                    self.permissions[perm_name] = permission
                
                logger.info(f"Loaded {len(self.permissions)} permissions")
                
        except Exception as e:
            logger.error(f"Error loading permissions: {e}")
    
    def _save_permissions(self):
        """Save permissions to file"""
        try:
            permissions_data = {}
            
            for perm_name, permission in self.permissions.items():
                perm_dict = {
                    "name": permission.name,
                    "description": permission.description,
                    "level": permission.level.name,
                    "resource_type": permission.resource_type,
                    "conditions": permission.conditions
                }
                permissions_data[perm_name] = perm_dict
            
            with open(self.permissions_file, 'w') as f:
                json.dump(permissions_data, f, indent=2)
                
        except Exception as e:
            logger.error(f"Error saving permissions: {e}")
    
    def _load_roles(self):
        """Load roles from file"""
        try:
            if self.roles_file.exists():
                with open(self.roles_file, 'r') as f:
                    roles_data = json.load(f)
                
                for role_id, role_dict in roles_data.items():
                    role_dict["permissions"] = set(role_dict["permissions"])
                    role_dict["created_at"] = datetime.fromisoformat(role_dict["created_at"])
                    role = Role(**role_dict)
                    self.roles[role_id] = role
                
                logger.info(f"Loaded {len(self.roles)} roles")
            
            # Load user roles
            user_roles_file = self.data_dir / "user_roles.json"
            if user_roles_file.exists():
                with open(user_roles_file, 'r') as f:
                    user_roles_data = json.load(f)
                
                for user_id, roles in user_roles_data.items():
                    self.user_roles[user_id] = set(roles)
                
                logger.info(f"Loaded roles for {len(self.user_roles)} users")
                
        except Exception as e:
            logger.error(f"Error loading roles: {e}")
    
    def _save_roles(self):
        """Save roles to file"""
        try:
            roles_data = {}
            
            for role_id, role in self.roles.items():
                role_dict = {
                    "role_id": role.role_id,
                    "name": role.name,
                    "description": role.description,
                    "permissions": list(role.permissions),
                    "is_system_role": role.is_system_role,
                    "created_at": role.created_at.isoformat(),
                    "metadata": role.metadata
                }
                roles_data[role_id] = role_dict
            
            with open(self.roles_file, 'w') as f:
                json.dump(roles_data, f, indent=2)
                
        except Exception as e:
            logger.error(f"Error saving roles: {e}")
