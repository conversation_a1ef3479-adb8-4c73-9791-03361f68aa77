"""
Encryption System for JARVIS AI Assistant
"""

import os
import base64
import secrets
import logging
from typing import Dict, Optional, Tuple, Union, Any
from pathlib import Path
import json
from datetime import datetime

# Optional cryptography imports with fallbacks
try:
    from cryptography.fernet import Fernet
    from cryptography.hazmat.primitives import hashes, serialization
    from cryptography.hazmat.primitives.asymmetric import rsa, padding
    from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
    from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
    CRYPTOGRAPHY_AVAILABLE = True
except ImportError:
    # Create dummy classes/functions for when cryptography is not available
    Fernet = None
    hashes = None
    serialization = None
    rsa = None
    padding = None
    PBKDF2HMAC = None
    Cipher = None
    algorithms = None
    modes = None
    CRYPTOGRAPHY_AVAILABLE = False

from ..config import config

logger = logging.getLogger(__name__)


class EncryptionManager:
    """Comprehensive encryption and cryptographic operations"""
    
    def __init__(self, data_dir: Optional[str] = None):
        """Initialize encryption manager"""
        self.data_dir = Path(data_dir or config.get("data_dir", "data"))
        self.keys_dir = self.data_dir / "keys"
        
        # Ensure directories exist
        self.data_dir.mkdir(exist_ok=True)
        self.keys_dir.mkdir(exist_ok=True, mode=0o700)  # Restricted permissions
        
        # Encryption keys
        self.master_key = None
        self.symmetric_key = None
        self.private_key = None
        self.public_key = None
        
        # Key files
        self.master_key_file = self.keys_dir / "master.key"
        self.symmetric_key_file = self.keys_dir / "symmetric.key"
        self.private_key_file = self.keys_dir / "private.pem"
        self.public_key_file = self.keys_dir / "public.pem"
        
        # Initialize encryption system
        self._initialize_encryption()
        
        logger.info("Encryption manager initialized")
    
    def _initialize_encryption(self):
        """Initialize encryption keys and system"""
        try:
            if not CRYPTOGRAPHY_AVAILABLE:
                logger.warning("Cryptography library not available - using basic encryption")
                self._initialize_basic_encryption()
                return

            # Load or generate master key
            self._load_or_generate_master_key()

            # Load or generate symmetric key
            self._load_or_generate_symmetric_key()

            # Load or generate asymmetric keys
            self._load_or_generate_asymmetric_keys()

            logger.info("Encryption system initialized successfully")

        except Exception as e:
            logger.error(f"Error initializing encryption: {e}")
            # Fall back to basic encryption
            self._initialize_basic_encryption()

    def _initialize_basic_encryption(self):
        """Initialize basic encryption when cryptography is not available"""
        self.master_key = secrets.token_bytes(32)
        self.symmetric_key = secrets.token_bytes(32)
        logger.info("Basic encryption initialized")
    
    def _load_or_generate_master_key(self):
        """Load or generate master encryption key"""
        try:
            if self.master_key_file.exists():
                # Load existing master key
                with open(self.master_key_file, 'rb') as f:
                    self.master_key = f.read()
                logger.info("Loaded existing master key")
            else:
                # Generate new master key
                self.master_key = secrets.token_bytes(32)  # 256-bit key
                
                # Save master key with restricted permissions
                with open(self.master_key_file, 'wb') as f:
                    f.write(self.master_key)
                os.chmod(self.master_key_file, 0o600)  # Owner read/write only
                
                logger.info("Generated new master key")
                
        except Exception as e:
            logger.error(f"Error with master key: {e}")
            raise
    
    def _load_or_generate_symmetric_key(self):
        """Load or generate symmetric encryption key"""
        if not CRYPTOGRAPHY_AVAILABLE:
            logger.warning("Cryptography library not available, using fallback key")
            self.symmetric_key = b'fallback_key_32_bytes_long_here!'
            return

        try:
            if self.symmetric_key_file.exists():
                # Load existing symmetric key
                with open(self.symmetric_key_file, 'rb') as f:
                    encrypted_key = f.read()

                # Decrypt symmetric key using master key
                self.symmetric_key = self._decrypt_with_master_key(encrypted_key)
                logger.info("Loaded existing symmetric key")
            else:
                # Generate new symmetric key
                if not CRYPTOGRAPHY_AVAILABLE:
                    # Fallback when cryptography is not available
                    key = base64.urlsafe_b64encode(secrets.token_bytes(32))
                else:
                    key = Fernet.generate_key()
                self.symmetric_key = key

                # Encrypt and save symmetric key
                encrypted_key = self._encrypt_with_master_key(key)
                with open(self.symmetric_key_file, 'wb') as f:
                    f.write(encrypted_key)
                os.chmod(self.symmetric_key_file, 0o600)

                logger.info("Generated new symmetric key")

        except Exception as e:
            logger.error(f"Error with symmetric key: {e}")
            raise
    
    def _load_or_generate_asymmetric_keys(self):
        """Load or generate asymmetric key pair"""
        if not CRYPTOGRAPHY_AVAILABLE:
            logger.warning("Cryptography library not available, asymmetric keys disabled")
            self.private_key = None
            self.public_key = None
            return

        try:
            if self.private_key_file.exists() and self.public_key_file.exists():
                # Load existing keys
                with open(self.private_key_file, 'rb') as f:
                    encrypted_private_key = f.read()

                with open(self.public_key_file, 'rb') as f:
                    public_key_pem = f.read()

                # Decrypt private key
                private_key_pem = self._decrypt_with_master_key(encrypted_private_key)

                # Load keys
                self.private_key = serialization.load_pem_private_key(
                    private_key_pem, password=None
                )
                self.public_key = serialization.load_pem_public_key(public_key_pem)

                logger.info("Loaded existing asymmetric keys")
            else:
                # Generate new key pair
                self.private_key = rsa.generate_private_key(
                    public_exponent=65537,
                    key_size=2048
                )
                self.public_key = self.private_key.public_key()

                # Serialize keys
                private_key_pem = self.private_key.private_bytes(
                    encoding=serialization.Encoding.PEM,
                    format=serialization.PrivateFormat.PKCS8,
                    encryption_algorithm=serialization.NoEncryption()
                )
                
                public_key_pem = self.public_key.public_bytes(
                    encoding=serialization.Encoding.PEM,
                    format=serialization.PublicFormat.SubjectPublicKeyInfo
                )
                
                # Encrypt and save private key
                encrypted_private_key = self._encrypt_with_master_key(private_key_pem)
                with open(self.private_key_file, 'wb') as f:
                    f.write(encrypted_private_key)
                os.chmod(self.private_key_file, 0o600)
                
                # Save public key
                with open(self.public_key_file, 'wb') as f:
                    f.write(public_key_pem)
                os.chmod(self.public_key_file, 0o644)
                
                logger.info("Generated new asymmetric keys")
                
        except Exception as e:
            logger.error(f"Error with asymmetric keys: {e}")
            raise
    
    def _encrypt_with_master_key(self, data: bytes) -> bytes:
        """Encrypt data with master key using AES-GCM"""
        if not CRYPTOGRAPHY_AVAILABLE:
            # Simple XOR fallback (not secure, for development only)
            logger.warning("Using insecure fallback encryption")
            key = self.master_key[:len(data)] if len(self.master_key) >= len(data) else (self.master_key * ((len(data) // len(self.master_key)) + 1))[:len(data)]
            return bytes(a ^ b for a, b in zip(data, key))

        try:
            # Generate random IV
            iv = secrets.token_bytes(12)  # 96-bit IV for GCM

            # Create cipher
            cipher = Cipher(
                algorithms.AES(self.master_key),
                modes.GCM(iv)
            )
            encryptor = cipher.encryptor()

            # Encrypt data
            ciphertext = encryptor.update(data) + encryptor.finalize()

            # Return IV + tag + ciphertext
            return iv + encryptor.tag + ciphertext

        except Exception as e:
            logger.error(f"Error encrypting with master key: {e}")
            raise
    
    def _decrypt_with_master_key(self, encrypted_data: bytes) -> bytes:
        """Decrypt data with master key using AES-GCM"""
        if not CRYPTOGRAPHY_AVAILABLE:
            # Simple XOR fallback (not secure, for development only)
            logger.warning("Using insecure fallback decryption")
            key = self.master_key[:len(encrypted_data)] if len(self.master_key) >= len(encrypted_data) else (self.master_key * ((len(encrypted_data) // len(self.master_key)) + 1))[:len(encrypted_data)]
            return bytes(a ^ b for a, b in zip(encrypted_data, key))

        try:
            # Extract IV, tag, and ciphertext
            iv = encrypted_data[:12]
            tag = encrypted_data[12:28]
            ciphertext = encrypted_data[28:]

            # Create cipher
            cipher = Cipher(
                algorithms.AES(self.master_key),
                modes.GCM(iv, tag)
            )
            decryptor = cipher.decryptor()

            # Decrypt data
            plaintext = decryptor.update(ciphertext) + decryptor.finalize()

            return plaintext

        except Exception as e:
            logger.error(f"Error decrypting with master key: {e}")
            raise
    
    def encrypt_data(self, data: Union[str, bytes]) -> str:
        """Encrypt data using symmetric encryption"""
        try:
            if isinstance(data, str):
                data = data.encode('utf-8')

            if CRYPTOGRAPHY_AVAILABLE:
                fernet = Fernet(self.symmetric_key)
                encrypted_data = fernet.encrypt(data)
                return base64.b64encode(encrypted_data).decode('utf-8')
            else:
                # Basic XOR encryption as fallback
                encrypted = bytearray()
                key_bytes = self.symmetric_key
                for i, byte in enumerate(data):
                    encrypted.append(byte ^ key_bytes[i % len(key_bytes)])
                return base64.b64encode(encrypted).decode('utf-8')

        except Exception as e:
            logger.error(f"Error encrypting data: {e}")
            raise
    
    def decrypt_data(self, encrypted_data: str) -> bytes:
        """Decrypt data using symmetric encryption"""
        try:
            # Decode base64
            encrypted_bytes = base64.b64decode(encrypted_data.encode('utf-8'))

            if CRYPTOGRAPHY_AVAILABLE:
                fernet = Fernet(self.symmetric_key)
                decrypted_data = fernet.decrypt(encrypted_bytes)
                return decrypted_data
            else:
                # Basic XOR decryption as fallback
                decrypted = bytearray()
                key_bytes = self.symmetric_key
                for i, byte in enumerate(encrypted_bytes):
                    decrypted.append(byte ^ key_bytes[i % len(key_bytes)])
                return bytes(decrypted)

        except Exception as e:
            logger.error(f"Error decrypting data: {e}")
            raise
    
    def encrypt_file(self, file_path: Union[str, Path], output_path: Optional[Union[str, Path]] = None) -> str:
        """Encrypt a file"""
        try:
            file_path = Path(file_path)
            if not file_path.exists():
                raise FileNotFoundError(f"File not found: {file_path}")
            
            # Read file data
            with open(file_path, 'rb') as f:
                file_data = f.read()
            
            # Encrypt data
            encrypted_data = self.encrypt_data(file_data)
            
            # Determine output path
            if output_path is None:
                output_path = file_path.with_suffix(file_path.suffix + '.enc')
            else:
                output_path = Path(output_path)
            
            # Write encrypted file
            with open(output_path, 'w') as f:
                f.write(encrypted_data)
            
            logger.info(f"Encrypted file: {file_path} -> {output_path}")
            return str(output_path)
            
        except Exception as e:
            logger.error(f"Error encrypting file: {e}")
            raise
    
    def decrypt_file(self, encrypted_file_path: Union[str, Path], output_path: Optional[Union[str, Path]] = None) -> str:
        """Decrypt a file"""
        try:
            encrypted_file_path = Path(encrypted_file_path)
            if not encrypted_file_path.exists():
                raise FileNotFoundError(f"Encrypted file not found: {encrypted_file_path}")
            
            # Read encrypted data
            with open(encrypted_file_path, 'r') as f:
                encrypted_data = f.read()
            
            # Decrypt data
            decrypted_data = self.decrypt_data(encrypted_data)
            
            # Determine output path
            if output_path is None:
                if encrypted_file_path.suffix == '.enc':
                    output_path = encrypted_file_path.with_suffix('')
                else:
                    output_path = encrypted_file_path.with_suffix('.dec')
            else:
                output_path = Path(output_path)
            
            # Write decrypted file
            with open(output_path, 'wb') as f:
                f.write(decrypted_data)
            
            logger.info(f"Decrypted file: {encrypted_file_path} -> {output_path}")
            return str(output_path)
            
        except Exception as e:
            logger.error(f"Error decrypting file: {e}")
            raise
    
    def encrypt_with_public_key(self, data: Union[str, bytes]) -> str:
        """Encrypt data with public key (asymmetric)"""
        if not CRYPTOGRAPHY_AVAILABLE or self.public_key is None:
            logger.warning("Asymmetric encryption not available, falling back to symmetric")
            return self.encrypt_data(data)

        try:
            if isinstance(data, str):
                data = data.encode('utf-8')

            # RSA can only encrypt small amounts of data
            if len(data) > 190:  # Conservative limit for 2048-bit key
                raise ValueError("Data too large for RSA encryption")

            encrypted_data = self.public_key.encrypt(
                data,
                padding.OAEP(
                    mgf=padding.MGF1(algorithm=hashes.SHA256()),
                    algorithm=hashes.SHA256(),
                    label=None
                )
            )

            return base64.b64encode(encrypted_data).decode('utf-8')

        except Exception as e:
            logger.error(f"Error encrypting with public key: {e}")
            raise
    
    def decrypt_with_private_key(self, encrypted_data: str) -> bytes:
        """Decrypt data with private key (asymmetric)"""
        if not CRYPTOGRAPHY_AVAILABLE or self.private_key is None:
            logger.warning("Asymmetric decryption not available, falling back to symmetric")
            return self.decrypt_data(encrypted_data)

        try:
            encrypted_bytes = base64.b64decode(encrypted_data.encode('utf-8'))

            decrypted_data = self.private_key.decrypt(
                encrypted_bytes,
                padding.OAEP(
                    mgf=padding.MGF1(algorithm=hashes.SHA256()),
                    algorithm=hashes.SHA256(),
                    label=None
                )
            )

            return decrypted_data

        except Exception as e:
            logger.error(f"Error decrypting with private key: {e}")
            raise
    
    def generate_hash(self, data: Union[str, bytes], algorithm: str = "sha256") -> str:
        """Generate cryptographic hash"""
        try:
            if isinstance(data, str):
                data = data.encode('utf-8')

            if not CRYPTOGRAPHY_AVAILABLE:
                # Fallback to hashlib
                import hashlib
                if algorithm.lower() == "sha256":
                    return hashlib.sha256(data).hexdigest()
                elif algorithm.lower() == "sha512":
                    return hashlib.sha512(data).hexdigest()
                elif algorithm.lower() == "md5":
                    return hashlib.md5(data).hexdigest()
                else:
                    raise ValueError(f"Unsupported hash algorithm: {algorithm}")

            if algorithm.lower() == "sha256":
                digest = hashes.Hash(hashes.SHA256())
            elif algorithm.lower() == "sha512":
                digest = hashes.Hash(hashes.SHA512())
            elif algorithm.lower() == "md5":
                digest = hashes.Hash(hashes.MD5())
            else:
                raise ValueError(f"Unsupported hash algorithm: {algorithm}")

            digest.update(data)
            hash_bytes = digest.finalize()

            return hash_bytes.hex()

        except Exception as e:
            logger.error(f"Error generating hash: {e}")
            raise
    
    def verify_hash(self, data: Union[str, bytes], expected_hash: str, algorithm: str = "sha256") -> bool:
        """Verify data against hash"""
        try:
            actual_hash = self.generate_hash(data, algorithm)
            return actual_hash.lower() == expected_hash.lower()
            
        except Exception as e:
            logger.error(f"Error verifying hash: {e}")
            return False
    
    def generate_secure_token(self, length: int = 32) -> str:
        """Generate cryptographically secure random token"""
        return secrets.token_urlsafe(length)
    
    def derive_key_from_password(self, password: str, salt: Optional[bytes] = None) -> Tuple[bytes, bytes]:
        """Derive encryption key from password using PBKDF2"""
        try:
            if salt is None:
                salt = secrets.token_bytes(16)

            if not CRYPTOGRAPHY_AVAILABLE:
                # Fallback to hashlib PBKDF2
                import hashlib
                key = hashlib.pbkdf2_hmac('sha256', password.encode('utf-8'), salt, 100000, 32)
                return key, salt

            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
            )

            key = kdf.derive(password.encode('utf-8'))
            return key, salt

        except Exception as e:
            logger.error(f"Error deriving key from password: {e}")
            raise
    
    def secure_delete_file(self, file_path: Union[str, Path]):
        """Securely delete a file by overwriting it"""
        try:
            file_path = Path(file_path)
            if not file_path.exists():
                return
            
            # Get file size
            file_size = file_path.stat().st_size
            
            # Overwrite with random data multiple times
            with open(file_path, 'r+b') as f:
                for _ in range(3):  # 3 passes
                    f.seek(0)
                    f.write(secrets.token_bytes(file_size))
                    f.flush()
                    os.fsync(f.fileno())
            
            # Finally delete the file
            file_path.unlink()
            
            logger.info(f"Securely deleted file: {file_path}")
            
        except Exception as e:
            logger.error(f"Error securely deleting file: {e}")
            raise
    
    def get_public_key_pem(self) -> str:
        """Get public key in PEM format"""
        if not CRYPTOGRAPHY_AVAILABLE or self.public_key is None:
            return ""

        public_key_pem = self.public_key.public_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PublicFormat.SubjectPublicKeyInfo
        )
        return public_key_pem.decode('utf-8')
    
    def backup_keys(self, backup_path: Union[str, Path], password: str):
        """Create encrypted backup of encryption keys"""
        try:
            backup_path = Path(backup_path)

            # Prepare key data
            key_data = {
                "master_key": base64.b64encode(self.master_key).decode('utf-8'),
                "symmetric_key": base64.b64encode(self.symmetric_key).decode('utf-8') if self.symmetric_key else None,
                "backup_date": datetime.now().isoformat()
            }

            # Add asymmetric keys if available
            if CRYPTOGRAPHY_AVAILABLE and self.private_key is not None:
                key_data["private_key"] = self.private_key.private_bytes(
                    encoding=serialization.Encoding.PEM,
                    format=serialization.PrivateFormat.PKCS8,
                    encryption_algorithm=serialization.NoEncryption()
                ).decode('utf-8')
                key_data["public_key"] = self.get_public_key_pem()

            # Serialize to JSON
            key_json = json.dumps(key_data, indent=2)

            # Derive key from password
            backup_key, salt = self.derive_key_from_password(password)

            # Encrypt backup data
            if CRYPTOGRAPHY_AVAILABLE:
                fernet = Fernet(base64.urlsafe_b64encode(backup_key))
                encrypted_backup = fernet.encrypt(key_json.encode('utf-8'))
            else:
                # Fallback encryption
                encrypted_backup = self._encrypt_with_master_key(key_json.encode('utf-8'))

            # Save backup
            backup_data = {
                "salt": base64.b64encode(salt).decode('utf-8'),
                "encrypted_keys": base64.b64encode(encrypted_backup).decode('utf-8')
            }

            with open(backup_path, 'w') as f:
                json.dump(backup_data, f, indent=2)

            logger.info(f"Created encrypted key backup: {backup_path}")

        except Exception as e:
            logger.error(f"Error creating key backup: {e}")
            raise
    
    def restore_keys(self, backup_path: Union[str, Path], password: str):
        """Restore encryption keys from encrypted backup"""
        try:
            backup_path = Path(backup_path)
            if not backup_path.exists():
                raise FileNotFoundError(f"Backup file not found: {backup_path}")

            # Load backup data
            with open(backup_path, 'r') as f:
                backup_data = json.load(f)

            # Extract salt and encrypted data
            salt = base64.b64decode(backup_data["salt"])
            encrypted_backup = base64.b64decode(backup_data["encrypted_keys"])

            # Derive key from password
            backup_key, _ = self.derive_key_from_password(password, salt)

            # Decrypt backup
            if CRYPTOGRAPHY_AVAILABLE:
                fernet = Fernet(base64.urlsafe_b64encode(backup_key))
                decrypted_data = fernet.decrypt(encrypted_backup)
            else:
                # Fallback decryption
                decrypted_data = self._decrypt_with_master_key(encrypted_backup)

            # Parse key data
            key_data = json.loads(decrypted_data.decode('utf-8'))

            # Restore keys
            self.master_key = base64.b64decode(key_data["master_key"])
            if key_data.get("symmetric_key"):
                self.symmetric_key = base64.b64decode(key_data["symmetric_key"])

            # Restore asymmetric keys if available
            if CRYPTOGRAPHY_AVAILABLE and "private_key" in key_data:
                # Restore private key
                private_key_pem = key_data["private_key"].encode('utf-8')
                self.private_key = serialization.load_pem_private_key(
                    private_key_pem, password=None
                )

                # Restore public key
                public_key_pem = key_data["public_key"].encode('utf-8')
                self.public_key = serialization.load_pem_public_key(public_key_pem)
            else:
                self.private_key = None
                self.public_key = None
            
            # Save restored keys
            self._save_all_keys()
            
            logger.info(f"Restored encryption keys from backup: {backup_path}")
            
        except Exception as e:
            logger.error(f"Error restoring keys from backup: {e}")
            raise
    
    def _save_all_keys(self):
        """Save all keys to their respective files"""
        # Save master key
        with open(self.master_key_file, 'wb') as f:
            f.write(self.master_key)
        os.chmod(self.master_key_file, 0o600)

        # Save encrypted symmetric key
        if self.symmetric_key:
            encrypted_symmetric = self._encrypt_with_master_key(self.symmetric_key)
            with open(self.symmetric_key_file, 'wb') as f:
                f.write(encrypted_symmetric)
            os.chmod(self.symmetric_key_file, 0o600)

        # Save encrypted private key if available
        if CRYPTOGRAPHY_AVAILABLE and self.private_key is not None:
            private_key_pem = self.private_key.private_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PrivateFormat.PKCS8,
                encryption_algorithm=serialization.NoEncryption()
            )
            encrypted_private = self._encrypt_with_master_key(private_key_pem)
            with open(self.private_key_file, 'wb') as f:
                f.write(encrypted_private)
            os.chmod(self.private_key_file, 0o600)

        # Save public key if available
        if CRYPTOGRAPHY_AVAILABLE and self.public_key is not None:
            public_key_pem = self.public_key.public_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PublicFormat.SubjectPublicKeyInfo
            )
            with open(self.public_key_file, 'wb') as f:
                f.write(public_key_pem)
            os.chmod(self.public_key_file, 0o644)
    
    def get_encryption_status(self) -> Dict[str, Any]:
        """Get encryption system status"""
        return {
            "master_key_exists": self.master_key is not None,
            "symmetric_key_exists": self.symmetric_key is not None,
            "private_key_exists": self.private_key is not None,
            "public_key_exists": self.public_key is not None,
            "keys_directory": str(self.keys_dir),
            "key_files": {
                "master_key": self.master_key_file.exists(),
                "symmetric_key": self.symmetric_key_file.exists(),
                "private_key": self.private_key_file.exists(),
                "public_key": self.public_key_file.exists()
            }
        }
