#!/usr/bin/env python3
"""
Test script to verify all imports are working correctly
"""

def test_imports():
    """Test all the imports used in web_interface.py"""
    
    print("Testing imports...")
    
    try:
        # Test basic imports
        import asyncio
        import logging
        import json
        from typing import Dict, Any, Optional
        from pathlib import Path
        print("✅ Basic imports successful")
        
        # Test aiohttp imports
        from aiohttp import web, WSMsgType
        print("✅ aiohttp imports successful")
        
        # Test aiohttp-cors import
        import aiohttp_cors
        print("✅ aiohttp_cors import successful")
        
        # Test socketio import
        import socketio
        print("✅ socketio import successful")
        
        # Test socketio functionality
        sio = socketio.AsyncServer()
        print("✅ socketio.AsyncServer creation successful")
        
        # Test weakref import
        import weakref
        print("✅ weakref import successful")
        
        # Test the actual web interface import
        from jarvis.interfaces.gui.web_interface import WebInterface
        print("✅ WebInterface import successful")
        
        # Test WebInterface instantiation
        web_interface = WebInterface()
        print("✅ WebInterface instantiation successful")
        
        print("\n🎉 All imports and basic functionality tests passed!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = test_imports()
    if success:
        print("\n✅ All tests passed! The import issues have been resolved.")
    else:
        print("\n❌ Some tests failed. Please check the error messages above.")
