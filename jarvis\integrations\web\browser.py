"""
Web Browser Integration for JARVIS AI Assistant
"""

import time
import logging
from typing import Dict, Any, Optional, TYPE_CHECKING
from pathlib import Path
import requests
from urllib.parse import urlparse

from ...config import config

if TYPE_CHECKING:
    from selenium.webdriver.chrome.webdriver import WebDriver

logger = logging.getLogger(__name__)


class WebBrowser:
    """Advanced web browser with automation capabilities"""

    def __init__(self):
        """Initialize web browser"""
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })

        self.selenium_driver: Optional['WebDriver'] = None
        self.headless = config.get("web.browser.headless", True)
        self.timeout = config.get("web.browser.timeout", 30)
        self.download_dir = Path("downloads")
        self.download_dir.mkdir(exist_ok=True)

        logger.info("Web browser initialized")
    
    def _init_selenium(self):
        """Initialize Selenium WebDriver (lazy loading)"""
        if self.selenium_driver:
            return True

        try:
            from selenium import webdriver
            from selenium.webdriver.chrome.options import Options

            chrome_options = Options()
            if self.headless:
                chrome_options.add_argument("--headless")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1920,1080")

            # Set download directory preferences
            download_prefs = {
                "download.default_directory": str(self.download_dir.absolute()),
                "download.prompt_for_download": False,
                "download.directory_upgrade": True,
                "safebrowsing.enabled": True
            }
            chrome_options.add_experimental_option("prefs", download_prefs)

            self.selenium_driver = webdriver.Chrome(options=chrome_options)
            self.selenium_driver.set_page_load_timeout(self.timeout)

            logger.info("Selenium WebDriver initialized")
            return True

        except ImportError:
            logger.warning("Selenium not available. Install with: pip install selenium")
            return False
        except Exception as e:
            logger.error(f"Failed to initialize Selenium: {e}")
            return False
    
    def get_page(self, url: str, use_selenium: bool = False) -> Dict[str, Any]:
        """
        Get web page content
        
        Args:
            url: URL to fetch
            use_selenium: Use Selenium for JavaScript-heavy pages
            
        Returns:
            Dictionary with page content and metadata
        """
        try:
            if use_selenium:
                return self._get_page_selenium(url)
            else:
                return self._get_page_requests(url)
                
        except Exception as e:
            logger.error(f"Error getting page {url}: {e}")
            return {
                "success": False,
                "error": str(e),
                "url": url
            }
    
    def _get_page_requests(self, url: str) -> Dict[str, Any]:
        """Get page using requests library"""
        try:
            response = self.session.get(url, timeout=self.timeout)
            response.raise_for_status()
            
            return {
                "success": True,
                "url": url,
                "final_url": response.url,
                "status_code": response.status_code,
                "content": response.text,
                "headers": dict(response.headers),
                "encoding": response.encoding,
                "size": len(response.content),
                "method": "requests"
            }
            
        except requests.RequestException as e:
            return {
                "success": False,
                "error": str(e),
                "url": url,
                "method": "requests"
            }
    
    def _get_page_selenium(self, url: str) -> Dict[str, Any]:
        """Get page using Selenium WebDriver"""
        if not self._init_selenium() or self.selenium_driver is None:
            return {
                "success": False,
                "error": "Selenium WebDriver not available",
                "url": url
            }

        try:
            self.selenium_driver.get(url)

            # Wait for page to load
            time.sleep(2)

            return {
                "success": True,
                "url": url,
                "final_url": self.selenium_driver.current_url,
                "title": self.selenium_driver.title,
                "content": self.selenium_driver.page_source,
                "method": "selenium"
            }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "url": url,
                "method": "selenium"
            }
    
    def search_google(self, query: str, num_results: int = 10) -> Dict[str, Any]:
        """
        Search Google and return results
        
        Args:
            query: Search query
            num_results: Number of results to return
            
        Returns:
            Dictionary with search results
        """
        try:
            # Use Google Custom Search API if available
            api_key = config.get_api_key("google_search")
            search_engine_id = config.get("web.search.google_cse_id")
            
            if api_key and search_engine_id:
                return self._search_google_api(query, num_results, api_key, search_engine_id)
            else:
                return self._search_google_scrape(query, num_results)
                
        except Exception as e:
            logger.error(f"Error searching Google: {e}")
            return {
                "success": False,
                "error": str(e),
                "query": query
            }
    
    def _search_google_api(self, query: str, num_results: int, api_key: str, cse_id: str) -> Dict[str, Any]:
        """Search using Google Custom Search API"""
        try:
            url = "https://www.googleapis.com/customsearch/v1"
            params = {
                "key": api_key,
                "cx": cse_id,
                "q": query,
                "num": min(num_results, 10)
            }
            
            response = self.session.get(url, params=params, timeout=self.timeout)
            response.raise_for_status()
            
            data = response.json()
            
            results = []
            for item in data.get("items", []):
                results.append({
                    "title": item.get("title", ""),
                    "url": item.get("link", ""),
                    "snippet": item.get("snippet", ""),
                    "display_link": item.get("displayLink", "")
                })
            
            return {
                "success": True,
                "query": query,
                "results": results,
                "total_results": data.get("searchInformation", {}).get("totalResults", "0"),
                "search_time": data.get("searchInformation", {}).get("searchTime", "0"),
                "method": "google_api"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "query": query,
                "method": "google_api"
            }
    
    def _search_google_scrape(self, query: str, num_results: int) -> Dict[str, Any]:
        """Search by scraping Google search results"""
        try:
            if not self._init_selenium() or self.selenium_driver is None:
                return {
                    "success": False,
                    "error": "Selenium required for Google scraping",
                    "query": query
                }

            from selenium.webdriver.common.by import By

            # Navigate to Google
            search_url = f"https://www.google.com/search?q={query}&num={num_results}"
            self.selenium_driver.get(search_url)

            time.sleep(2)

            # Extract search results
            results = []
            result_elements = self.selenium_driver.find_elements(By.CSS_SELECTOR, "div.g")

            for element in result_elements[:num_results]:
                try:
                    title_elem = element.find_element(By.CSS_SELECTOR, "h3")
                    link_elem = element.find_element(By.CSS_SELECTOR, "a")
                    snippet_elem = element.find_element(By.CSS_SELECTOR, "span")

                    results.append({
                        "title": title_elem.text,
                        "url": link_elem.get_attribute("href"),
                        "snippet": snippet_elem.text
                    })
                except:
                    continue

            return {
                "success": True,
                "query": query,
                "results": results,
                "method": "google_scrape"
            }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "query": query,
                "method": "google_scrape"
            }
    
    def download_file(self, url: str, filename: Optional[str] = None) -> Dict[str, Any]:
        """
        Download file from URL

        Args:
            url: URL to download
            filename: Optional filename (auto-detected if not provided)

        Returns:
            Dictionary with download result
        """
        try:
            response = self.session.get(url, stream=True, timeout=self.timeout)
            response.raise_for_status()

            # Determine filename
            if not filename:
                # Try to get filename from Content-Disposition header
                content_disposition = response.headers.get('Content-Disposition', '')
                if 'filename=' in content_disposition:
                    filename = content_disposition.split('filename=')[1].strip('"')
                else:
                    # Extract from URL
                    filename = Path(urlparse(url).path).name or "download"

            # Ensure filename is a string at this point
            assert isinstance(filename, str), "Filename must be a string"
            file_path = self.download_dir / filename
            
            # Download file
            with open(file_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            return {
                "success": True,
                "url": url,
                "filename": filename,
                "file_path": str(file_path),
                "size": file_path.stat().st_size,
                "content_type": response.headers.get('Content-Type', 'unknown')
            }
            
        except Exception as e:
            logger.error(f"Error downloading file {url}: {e}")
            return {
                "success": False,
                "error": str(e),
                "url": url
            }
    
    def take_screenshot(self, url: str, filename: Optional[str] = None) -> Dict[str, Any]:
        """
        Take screenshot of web page

        Args:
            url: URL to screenshot
            filename: Optional filename

        Returns:
            Dictionary with screenshot result
        """
        if not self._init_selenium() or self.selenium_driver is None:
            return {
                "success": False,
                "error": "Selenium required for screenshots"
            }

        try:
            self.selenium_driver.get(url)
            time.sleep(3)  # Wait for page to load

            if not filename:
                filename = f"screenshot_{int(time.time())}.png"

            screenshot_path = self.download_dir / filename

            if self.selenium_driver.save_screenshot(str(screenshot_path)):
                return {
                    "success": True,
                    "url": url,
                    "filename": filename,
                    "file_path": str(screenshot_path),
                    "size": screenshot_path.stat().st_size
                }
            else:
                return {
                    "success": False,
                    "error": "Failed to save screenshot"
                }

        except Exception as e:
            logger.error(f"Error taking screenshot of {url}: {e}")
            return {
                "success": False,
                "error": str(e),
                "url": url
            }
    
    def close(self):
        """Close browser and cleanup resources"""
        if self.selenium_driver:
            try:
                self.selenium_driver.quit()
                self.selenium_driver = None
                logger.info("Selenium WebDriver closed")
            except Exception as e:
                logger.error(f"Error closing Selenium: {e}")
        
        self.session.close()
    
    def get_stats(self) -> Dict[str, Any]:
        """Get browser statistics"""
        return {
            "selenium_initialized": self.selenium_driver is not None,
            "headless_mode": self.headless,
            "timeout": self.timeout,
            "download_directory": str(self.download_dir),
            "session_cookies": len(self.session.cookies)
        }
    
    def __del__(self):
        """Cleanup on destruction"""
        self.close()
