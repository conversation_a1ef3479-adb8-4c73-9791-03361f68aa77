"""
Web Interface for JARVIS AI Assistant
"""

import asyncio
import logging
import json
from typing import Dict, Any, Optional
from pathlib import Path
from aiohttp import web, WSMsgType
import aiohttp_cors
import socketio

from ...config import config

logger = logging.getLogger(__name__)


class WebInterface:
    """Web-based interface for JARVIS using Socket.IO"""
    
    def __init__(self, jarvis_core=None):
        """Initialize web interface"""
        self.jarvis_core = jarvis_core
        self.app = web.Application()
        self.sio = socketio.AsyncServer(cors_allowed_origins="*")
        self.sio.attach(self.app)
        
        self.host = config.get("gui.web.host", "localhost")
        self.port = config.get("gui.web.port", 8080)
        self.static_path = Path(__file__).parent / "static"
        
        # Connected clients (session IDs)
        self.clients = set()
        
        # Setup routes and socket handlers
        self._setup_routes()
        self._setup_socket_handlers()
        
        # Create static files
        self._create_static_files()
        
        logger.info("Web interface initialized")
    
    def _setup_routes(self):
        """Setup HTTP routes"""
        # Serve static files
        self.app.router.add_static('/', self.static_path, name='static')
        
        # API routes
        self.app.router.add_get('/api/status', self._handle_status)
        self.app.router.add_post('/api/message', self._handle_message)
        self.app.router.add_get('/api/system', self._handle_system_info)
        
        # Setup CORS
        cors = aiohttp_cors.setup(self.app, defaults={
            "*": aiohttp_cors.ResourceOptions(
                allow_credentials=True,
                expose_headers="*",
                allow_headers="*",
                allow_methods="*"
            )
        })
        
        # Add CORS to all routes
        for route in list(self.app.router.routes()):
            cors.add(route)
    
    def _setup_socket_handlers(self):
        """Setup Socket.IO event handlers"""

        @self.sio.event
        async def connect(sid, environ):
            """Handle client connection"""
            logger.info(f"Client connected: {sid}")
            # Track the connected client
            self.clients.add(sid)
            await self.sio.emit('status', {
                'connected': True,
                'message': 'Connected to JARVIS'
            }, room=sid)

        @self.sio.event
        async def disconnect(sid):
            """Handle client disconnection"""
            logger.info(f"Client disconnected: {sid}")
            # Remove client from tracking (WeakSet will handle if already gone)
            self.clients.discard(sid)
        
        @self.sio.event
        async def message(sid, data):
            """Handle incoming messages"""
            try:
                user_message = data.get('message', '')
                if not user_message:
                    return
                
                logger.info(f"Received message from {sid}: {user_message}")
                
                # Process message through JARVIS core
                if self.jarvis_core:
                    response = await self.jarvis_core.process_message(user_message, session_id=sid)
                else:
                    response = f"Echo: {user_message}"
                
                # Send response back to client
                await self.sio.emit('response', {
                    'message': response,
                    'timestamp': asyncio.get_event_loop().time()
                }, room=sid)
                
            except Exception as e:
                logger.error(f"Error processing message: {e}")
                await self.sio.emit('error', {
                    'message': 'Sorry, I encountered an error processing your message.'
                }, room=sid)
        
        @self.sio.event
        async def voice_command(sid, data):
            """Handle voice commands"""
            try:
                command = data.get('command', '')
                if command:
                    # Process voice command
                    response = await self._process_voice_command(command, sid)
                    await self.sio.emit('voice_response', response, room=sid)
                    
            except Exception as e:
                logger.error(f"Error processing voice command: {e}")
        
        @self.sio.event
        async def get_system_status(sid, data):
            """Handle system status requests"""
            try:
                status = await self._get_system_status()
                await self.sio.emit('system_status', status, room=sid)
                
            except Exception as e:
                logger.error(f"Error getting system status: {e}")
    
    def _create_static_files(self):
        """Create static HTML, CSS, and JS files"""
        self.static_path.mkdir(exist_ok=True)
        
        # Create index.html
        index_html = '''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JARVIS AI Assistant</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div id="app">
        <header class="header">
            <div class="header-content">
                <h1 class="logo">
                    <span class="logo-icon">🤖</span>
                    JARVIS
                </h1>
                <div class="status-indicators">
                    <div class="status-item" id="connection-status">
                        <span class="material-icons">wifi</span>
                        <span>Connecting...</span>
                    </div>
                    <div class="status-item" id="voice-status">
                        <span class="material-icons">mic_off</span>
                        <span>Voice Off</span>
                    </div>
                </div>
            </div>
        </header>

        <main class="main-content">
            <div class="chat-container">
                <div class="messages" id="messages">
                    <div class="message jarvis-message">
                        <div class="message-avatar">
                            <span class="material-icons">smart_toy</span>
                        </div>
                        <div class="message-content">
                            <div class="message-text">
                                Hello! I'm JARVIS, your AI assistant. How can I help you today?
                            </div>
                            <div class="message-time">
                                <span id="initial-time"></span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="input-container">
                    <div class="input-wrapper">
                        <input type="text" id="message-input" placeholder="Type your message to JARVIS..." />
                        <button id="voice-button" class="voice-button">
                            <span class="material-icons">mic</span>
                        </button>
                        <button id="send-button" class="send-button">
                            <span class="material-icons">send</span>
                        </button>
                    </div>
                </div>
            </div>

            <div class="sidebar">
                <div class="sidebar-section">
                    <h3>System Status</h3>
                    <div class="status-grid">
                        <div class="status-card">
                            <div class="status-label">CPU</div>
                            <div class="status-value" id="cpu-usage">0%</div>
                        </div>
                        <div class="status-card">
                            <div class="status-label">Memory</div>
                            <div class="status-value" id="memory-usage">0%</div>
                        </div>
                        <div class="status-card">
                            <div class="status-label">Network</div>
                            <div class="status-value" id="network-status">Connected</div>
                        </div>
                    </div>
                </div>

                <div class="sidebar-section">
                    <h3>Quick Actions</h3>
                    <div class="quick-actions">
                        <button class="action-button" onclick="sendQuickMessage('What time is it?')">
                            <span class="material-icons">schedule</span>
                            Time
                        </button>
                        <button class="action-button" onclick="sendQuickMessage('Show system usage')">
                            <span class="material-icons">computer</span>
                            System
                        </button>
                        <button class="action-button" onclick="sendQuickMessage('Search for AI news')">
                            <span class="material-icons">search</span>
                            Search
                        </button>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="https://cdn.socket.io/4.6.0/socket.io.min.js"></script>
    <script src="app.js"></script>
</body>
</html>'''
        
        with open(self.static_path / "index.html", 'w') as f:
            f.write(index_html)
        
        # Create CSS file
        self._create_css_file()
        
        # Create JavaScript file
        self._create_js_file()
    
    def _create_css_file(self):
        """Create CSS styles"""
        css_content = '''* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
    color: #ffffff;
    height: 100vh;
    overflow: hidden;
}

#app {
    height: 100vh;
    display: flex;
    flex-direction: column;
}

.header {
    background: rgba(0, 0, 0, 0.8);
    border-bottom: 1px solid #333;
    padding: 1rem 2rem;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.5rem;
    font-weight: 700;
    color: #00bcd4;
}

.logo-icon {
    font-size: 2rem;
}

.status-indicators {
    display: flex;
    gap: 1rem;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.875rem;
    opacity: 0.8;
}

.main-content {
    flex: 1;
    display: flex;
    overflow: hidden;
}

.chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: rgba(0, 0, 0, 0.3);
}

.messages {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.message {
    display: flex;
    gap: 0.75rem;
    max-width: 80%;
}

.user-message {
    align-self: flex-end;
    flex-direction: row-reverse;
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #00bcd4;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.user-message .message-avatar {
    background: #ff4081;
}

.message-content {
    flex: 1;
}

.message-text {
    background: rgba(255, 255, 255, 0.1);
    padding: 0.75rem 1rem;
    border-radius: 1rem;
    line-height: 1.5;
}

.user-message .message-text {
    background: #ff4081;
    color: white;
}

.message-time {
    font-size: 0.75rem;
    opacity: 0.6;
    margin-top: 0.25rem;
    padding: 0 1rem;
}

.input-container {
    padding: 1rem;
    background: rgba(0, 0, 0, 0.5);
    border-top: 1px solid #333;
}

.input-wrapper {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

#message-input {
    flex: 1;
    padding: 0.75rem 1rem;
    border: 1px solid #333;
    border-radius: 2rem;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 1rem;
    outline: none;
}

#message-input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

#message-input:focus {
    border-color: #00bcd4;
}

.voice-button, .send-button {
    width: 48px;
    height: 48px;
    border: none;
    border-radius: 50%;
    background: #00bcd4;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
}

.voice-button:hover, .send-button:hover {
    background: #0097a7;
    transform: scale(1.05);
}

.voice-button.active {
    background: #f44336;
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.sidebar {
    width: 300px;
    background: rgba(0, 0, 0, 0.5);
    border-left: 1px solid #333;
    padding: 1rem;
    overflow-y: auto;
}

.sidebar-section {
    margin-bottom: 2rem;
}

.sidebar-section h3 {
    margin-bottom: 1rem;
    color: #00bcd4;
    font-weight: 500;
}

.status-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
}

.status-card {
    background: rgba(255, 255, 255, 0.1);
    padding: 1rem;
    border-radius: 0.5rem;
    text-align: center;
}

.status-label {
    font-size: 0.75rem;
    opacity: 0.8;
    margin-bottom: 0.25rem;
}

.status-value {
    font-size: 1.25rem;
    font-weight: 500;
    color: #00bcd4;
}

.quick-actions {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.action-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    border: 1px solid #333;
    border-radius: 0.5rem;
    background: rgba(255, 255, 255, 0.05);
    color: white;
    cursor: pointer;
    transition: all 0.2s;
}

.action-button:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: #00bcd4;
}

.typing-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    opacity: 0.7;
    font-style: italic;
}

.typing-dots {
    display: flex;
    gap: 0.25rem;
}

.typing-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #00bcd4;
    animation: typing 1.4s infinite;
}

.typing-dot:nth-child(2) { animation-delay: 0.2s; }
.typing-dot:nth-child(3) { animation-delay: 0.4s; }

@keyframes typing {
    0%, 60%, 100% { opacity: 0.3; }
    30% { opacity: 1; }
}'''
        
        with open(self.static_path / "style.css", 'w') as f:
            f.write(css_content)
    
    def _create_js_file(self):
        """Create JavaScript application"""
        js_content = '''class JarvisWebInterface {
    constructor() {
        this.socket = io();
        this.isListening = false;
        this.recognition = null;
        
        this.initializeElements();
        this.setupSocketListeners();
        this.setupEventListeners();
        this.initializeSpeechRecognition();
        this.updateInitialTime();
    }

    initializeElements() {
        this.messagesContainer = document.getElementById('messages');
        this.messageInput = document.getElementById('message-input');
        this.sendButton = document.getElementById('send-button');
        this.voiceButton = document.getElementById('voice-button');
        this.connectionStatus = document.getElementById('connection-status');
        this.voiceStatus = document.getElementById('voice-status');
    }

    setupSocketListeners() {
        this.socket.on('connect', () => {
            this.updateConnectionStatus(true);
        });

        this.socket.on('disconnect', () => {
            this.updateConnectionStatus(false);
        });

        this.socket.on('response', (data) => {
            this.addMessage(data.message, 'jarvis');
            this.removeTypingIndicator();
        });

        this.socket.on('system_status', (data) => {
            this.updateSystemStatus(data);
        });

        this.socket.on('error', (data) => {
            this.addMessage(data.message, 'jarvis');
            this.removeTypingIndicator();
        });
    }

    setupEventListeners() {
        this.sendButton.addEventListener('click', () => this.sendMessage());
        this.messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });

        this.voiceButton.addEventListener('click', () => this.toggleVoice());

        // Request system status periodically
        setInterval(() => {
            this.socket.emit('get_system_status');
        }, 5000);
    }

    initializeSpeechRecognition() {
        if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            this.recognition = new SpeechRecognition();
            this.recognition.continuous = false;
            this.recognition.interimResults = false;
            this.recognition.lang = 'en-US';

            this.recognition.onresult = (event) => {
                const transcript = event.results[0][0].transcript;
                this.messageInput.value = transcript;
                this.sendMessage();
            };

            this.recognition.onend = () => {
                this.isListening = false;
                this.updateVoiceStatus();
            };

            this.recognition.onerror = (event) => {
                console.error('Speech recognition error:', event.error);
                this.isListening = false;
                this.updateVoiceStatus();
            };
        }
    }

    sendMessage() {
        const message = this.messageInput.value.trim();
        if (!message) return;

        this.addMessage(message, 'user');
        this.messageInput.value = '';
        this.addTypingIndicator();

        this.socket.emit('message', { message });
    }

    toggleVoice() {
        if (!this.recognition) {
            alert('Speech recognition is not supported in this browser.');
            return;
        }

        if (this.isListening) {
            this.recognition.stop();
        } else {
            this.recognition.start();
            this.isListening = true;
        }
        
        this.updateVoiceStatus();
    }

    addMessage(text, sender) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}-message`;

        const avatar = document.createElement('div');
        avatar.className = 'message-avatar';
        avatar.innerHTML = sender === 'user' 
            ? '<span class="material-icons">person</span>'
            : '<span class="material-icons">smart_toy</span>';

        const content = document.createElement('div');
        content.className = 'message-content';

        const messageText = document.createElement('div');
        messageText.className = 'message-text';
        messageText.textContent = text;

        const messageTime = document.createElement('div');
        messageTime.className = 'message-time';
        messageTime.textContent = new Date().toLocaleTimeString();

        content.appendChild(messageText);
        content.appendChild(messageTime);
        messageDiv.appendChild(avatar);
        messageDiv.appendChild(content);

        this.messagesContainer.appendChild(messageDiv);
        this.scrollToBottom();
    }

    addTypingIndicator() {
        const typingDiv = document.createElement('div');
        typingDiv.className = 'message jarvis-message typing-indicator';
        typingDiv.id = 'typing-indicator';

        const avatar = document.createElement('div');
        avatar.className = 'message-avatar';
        avatar.innerHTML = '<span class="material-icons">smart_toy</span>';

        const content = document.createElement('div');
        content.className = 'message-content';

        const typingText = document.createElement('div');
        typingText.className = 'message-text';
        typingText.innerHTML = `
            JARVIS is thinking...
            <div class="typing-dots">
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
            </div>
        `;

        content.appendChild(typingText);
        typingDiv.appendChild(avatar);
        typingDiv.appendChild(content);

        this.messagesContainer.appendChild(typingDiv);
        this.scrollToBottom();
    }

    removeTypingIndicator() {
        const typingIndicator = document.getElementById('typing-indicator');
        if (typingIndicator) {
            typingIndicator.remove();
        }
    }

    updateConnectionStatus(connected) {
        const statusSpan = this.connectionStatus.querySelector('span:last-child');
        const iconSpan = this.connectionStatus.querySelector('.material-icons');
        
        if (connected) {
            statusSpan.textContent = 'Connected';
            iconSpan.textContent = 'wifi';
            this.connectionStatus.style.color = '#4caf50';
        } else {
            statusSpan.textContent = 'Disconnected';
            iconSpan.textContent = 'wifi_off';
            this.connectionStatus.style.color = '#f44336';
        }
    }

    updateVoiceStatus() {
        const statusSpan = this.voiceStatus.querySelector('span:last-child');
        const iconSpan = this.voiceStatus.querySelector('.material-icons');
        
        if (this.isListening) {
            statusSpan.textContent = 'Listening';
            iconSpan.textContent = 'mic';
            this.voiceStatus.style.color = '#f44336';
            this.voiceButton.classList.add('active');
        } else {
            statusSpan.textContent = 'Voice Off';
            iconSpan.textContent = 'mic_off';
            this.voiceStatus.style.color = '#666';
            this.voiceButton.classList.remove('active');
        }
    }

    updateSystemStatus(status) {
        if (status.cpu !== undefined) {
            document.getElementById('cpu-usage').textContent = `${Math.round(status.cpu)}%`;
        }
        if (status.memory !== undefined) {
            document.getElementById('memory-usage').textContent = `${Math.round(status.memory)}%`;
        }
        if (status.network !== undefined) {
            document.getElementById('network-status').textContent = status.network;
        }
    }

    updateInitialTime() {
        document.getElementById('initial-time').textContent = new Date().toLocaleTimeString();
    }

    scrollToBottom() {
        this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
    }
}

// Quick message function
function sendQuickMessage(message) {
    if (window.jarvisInterface) {
        window.jarvisInterface.messageInput.value = message;
        window.jarvisInterface.sendMessage();
    }
}

// Initialize the interface when the page loads
document.addEventListener('DOMContentLoaded', () => {
    window.jarvisInterface = new JarvisWebInterface();
});'''
        
        with open(self.static_path / "app.js", 'w') as f:
            f.write(js_content)
    
    async def _handle_status(self, request):
        """Handle status API endpoint"""
        return web.json_response({
            "status": "running",
            "clients": len(self.clients),
            "jarvis_available": self.jarvis_core is not None
        })
    
    async def _handle_message(self, request):
        """Handle message API endpoint"""
        try:
            data = await request.json()
            message = data.get('message', '')
            
            if self.jarvis_core:
                response = await self.jarvis_core.process_message(message)
            else:
                response = f"Echo: {message}"
            
            return web.json_response({
                "success": True,
                "response": response
            })
            
        except Exception as e:
            return web.json_response({
                "success": False,
                "error": str(e)
            }, status=500)
    
    async def _handle_system_info(self, request):
        """Handle system info API endpoint"""
        try:
            status = await self._get_system_status()
            return web.json_response(status)
            
        except Exception as e:
            return web.json_response({
                "error": str(e)
            }, status=500)
    
    async def _process_voice_command(self, command: str, session_id: str) -> Dict[str, Any]:
        """Process voice command"""
        try:
            if self.jarvis_core:
                response = await self.jarvis_core.process_message(command, session_id=session_id)
            else:
                response = f"Voice command received: {command}"
            
            return {
                "success": True,
                "response": response,
                "command": command
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _get_system_status(self) -> Dict[str, Any]:
        """Get system status information"""
        try:
            # This would integrate with the system monitor
            import psutil
            
            return {
                "cpu": psutil.cpu_percent(),
                "memory": psutil.virtual_memory().percent,
                "network": "connected",
                "timestamp": asyncio.get_event_loop().time()
            }
            
        except ImportError:
            # Fallback if psutil not available
            return {
                "cpu": 0,
                "memory": 0,
                "network": "unknown",
                "timestamp": asyncio.get_event_loop().time()
            }
    
    async def start(self):
        """Start the web interface"""
        try:
            runner = web.AppRunner(self.app)
            await runner.setup()
            
            site = web.TCPSite(runner, self.host, self.port)
            await site.start()
            
            logger.info(f"Web interface started at http://{self.host}:{self.port}")
            
            return {
                "success": True,
                "message": f"Web interface started at http://{self.host}:{self.port}",
                "host": self.host,
                "port": self.port
            }
            
        except Exception as e:
            logger.error(f"Error starting web interface: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def stop(self):
        """Stop the web interface"""
        try:
            # Disconnect all connected clients
            clients_to_disconnect = list(self.clients)  # Create a copy to avoid modification during iteration
            for sid in clients_to_disconnect:
                try:
                    await self.sio.disconnect(sid)
                except Exception as e:
                    logger.warning(f"Error disconnecting client {sid}: {e}")

            # Clear the clients set
            self.clients.clear()

            logger.info("Web interface stopped")

            return {
                "success": True,
                "message": "Web interface stopped"
            }
            
        except Exception as e:
            logger.error(f"Error stopping web interface: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def get_status(self) -> Dict[str, Any]:
        """Get interface status"""
        return {
            "host": self.host,
            "port": self.port,
            "static_path": str(self.static_path),
            "clients_connected": len(self.clients),
            "jarvis_core_available": self.jarvis_core is not None
        }
